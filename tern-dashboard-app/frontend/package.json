{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.4", "@mui/material": "^7.3.4", "@tanstack/react-query": "^5.90.2", "@tanstack/react-table": "^8.21.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-virtuoso": "^4.14.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/node": "^24.6.0", "@types/react": "^19.1.16", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.4", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.22", "globals": "^16.4.0", "typescript": "~5.9.3", "typescript-eslint": "^8.45.0", "vite": "^7.1.7"}}