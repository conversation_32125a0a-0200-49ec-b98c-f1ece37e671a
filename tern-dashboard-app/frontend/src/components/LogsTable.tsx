import React, { useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  Pagination,
} from '@mui/material';
import { TableVirtuoso } from 'react-virtuoso';
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { useLogsStore } from '../store/logsStore';
import { useLogs } from '../hooks/useLogs';
import type { LogEntry } from '../types/logs';

const columnHelper = createColumnHelper<LogEntry>();

const LogsTable: React.FC = () => {
  const { filters, setFilters, selectedLogId, setSelectedLogId } = useLogsStore();
  const { data, isLoading, error, isFetching } = useLogs(filters);

  const columns = useMemo(
    () => [
      columnHelper.accessor('timestamp', {
        header: 'Timestamp',
        size: 180,
        cell: (info) => {
          const date = new Date(info.getValue());
          return (
            <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
              {date.toLocaleString()}
            </Typography>
          );
        },
      }),
      columnHelper.accessor('level', {
        header: 'Level',
        size: 80,
        cell: (info) => {
          const level = info.getValue();
          const color = 
            level === 'ERROR' ? 'error' :
            level === 'WARN' ? 'warning' :
            level === 'INFO' ? 'info' :
            'default';
          
          return (
            <Chip
              label={level}
              size="small"
              color={color as any}
              variant="outlined"
              sx={{ fontSize: '0.65rem', height: '20px' }}
            />
          );
        },
      }),
      columnHelper.accessor('process', {
        header: 'Process',
        size: 120,
        cell: (info) => (
          <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
            {info.getValue()}
          </Typography>
        ),
      }),
      columnHelper.accessor('position', {
        header: 'Position',
        size: 80,
        cell: (info) => (
          <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
            {info.getValue().toLocaleString()}
          </Typography>
        ),
      }),
      columnHelper.accessor('message', {
        header: 'Message',
        size: 600,
        cell: (info) => (
          <Typography 
            variant="body2" 
            sx={{ 
              fontFamily: 'monospace', 
              fontSize: '0.75rem',
              wordBreak: 'break-word',
              whiteSpace: 'pre-wrap'
            }}
          >
            {info.getValue()}
          </Typography>
        ),
      }),
    ],
    []
  );

  const table = useReactTable({
    data: data?.logs || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handlePageChange = (_: React.ChangeEvent<unknown>, page: number) => {
    setFilters({ page });
  };

  const handleRowClick = (logEntry: LogEntry) => {
    setSelectedLogId(selectedLogId === logEntry.id ? null : logEntry.id);
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        Error loading logs: {error instanceof Error ? error.message : 'Unknown error'}
      </Alert>
    );
  }

  if (!data || data.logs.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          No logs found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Try adjusting your filters or check if the backend is running.
        </Typography>
      </Paper>
    );
  }

  const totalPages = Math.ceil(data.total / (filters.page_size || 100));

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header with stats */}
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Showing {data.logs.length} of {data.total.toLocaleString()} logs
          {isFetching && <CircularProgress size={16} sx={{ ml: 1 }} />}
        </Typography>
        
        <Pagination
          count={totalPages}
          page={filters.page || 1}
          onChange={handlePageChange}
          size="small"
          showFirstButton
          showLastButton
        />
      </Box>

      {/* Virtualized Table */}
      <Paper sx={{ flex: 1, overflow: 'hidden' }}>
        <TableContainer sx={{ height: '100%' }}>
          <TableVirtuoso
            style={{ height: '100%' }}
            data={data.logs}
            components={{
              Scroller: React.forwardRef<HTMLDivElement>((props, ref) => (
                <div {...props} ref={ref} />
              )),
              Table: (props) => (
                <Table {...props} sx={{ borderCollapse: 'separate', tableLayout: 'fixed' }} />
              ),
              TableHead: React.forwardRef<HTMLTableSectionElement>((props, ref) => (
                <TableHead {...props} ref={ref} />
              )),
              TableRow: ({ item: _item, ...props }) => <TableRow {...props} />,
              TableBody: React.forwardRef<HTMLTableSectionElement>((props, ref) => (
                <tbody {...props} ref={ref} />
              )),
            }}
            fixedHeaderContent={() => (
              <TableRow>
                {table.getHeaderGroups()[0]?.headers.map((header) => (
                  <TableCell
                    key={header.id}
                    sx={{
                      width: header.getSize(),
                      backgroundColor: 'background.paper',
                      borderBottom: 2,
                      borderColor: 'divider',
                      fontWeight: 'bold',
                      position: 'sticky',
                      top: 0,
                      zIndex: 1,
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            )}
            itemContent={(index, logEntry) => {
              const row = table.getRowModel().rows[index];
              const isSelected = selectedLogId === logEntry.id;
              
              return (
                <>
                  {row?.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      sx={{
                        width: cell.column.getSize(),
                        cursor: 'pointer',
                        backgroundColor: isSelected ? 'action.selected' : 'inherit',
                        '&:hover': {
                          backgroundColor: 'action.hover',
                        },
                      }}
                      onClick={() => handleRowClick(logEntry)}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </>
              );
            }}
          />
        </TableContainer>
      </Paper>

      {/* Bottom pagination */}
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
        <Pagination
          count={totalPages}
          page={filters.page || 1}
          onChange={handlePageChange}
          size="small"
          showFirstButton
          showLastButton
        />
      </Box>
    </Box>
  );
};

export default LogsTable;
