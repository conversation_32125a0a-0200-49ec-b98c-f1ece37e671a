import React from 'react';
import {
  Box,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Paper,
  Chip,
} from '@mui/material';
import { Clear as ClearIcon } from '@mui/icons-material';
import { useLogsStore } from '../store/logsStore';
import { useLogLevels, useLogProcesses } from '../hooks/useLogs';

const LogsFilters: React.FC = () => {
  const { filters, setFilters, resetFilters } = useLogsStore();
  const { data: levelsData } = useLogLevels();
  const { data: processesData } = useLogProcesses();

  const handleFilterChange = (field: string, value: string | undefined) => {
    setFilters({ [field]: value || undefined });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ search: event.target.value || undefined });
  };

  const hasActiveFilters = Boolean(
    filters.level_filter || 
    filters.process_filter || 
    filters.search ||
    filters.start_time ||
    filters.end_time
  );

  return (
    <Paper elevation={1} sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
        <Box sx={{ minWidth: 200, flex: '1 1 200px' }}>
          <TextField
            fullWidth
            size="small"
            label="Search messages"
            value={filters.search || ''}
            onChange={handleSearchChange}
            placeholder="Enter search term..."
          />
        </Box>

        <Box sx={{ minWidth: 120, flex: '0 1 120px' }}>
          <FormControl fullWidth size="small">
            <InputLabel>Level</InputLabel>
            <Select
              value={filters.level_filter || ''}
              label="Level"
              onChange={(e) => handleFilterChange('level_filter', e.target.value)}
            >
              <MenuItem value="">All Levels</MenuItem>
              {levelsData?.levels.map((level) => (
                <MenuItem key={level} value={level}>
                  {level}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Box sx={{ minWidth: 120, flex: '0 1 120px' }}>
          <FormControl fullWidth size="small">
            <InputLabel>Process</InputLabel>
            <Select
              value={filters.process_filter || ''}
              label="Process"
              onChange={(e) => handleFilterChange('process_filter', e.target.value)}
            >
              <MenuItem value="">All Processes</MenuItem>
              {processesData?.processes.map((process) => (
                <MenuItem key={process} value={process}>
                  {process}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Box sx={{ minWidth: 160, flex: '0 1 160px' }}>
          <TextField
            fullWidth
            size="small"
            type="datetime-local"
            label="Start Time"
            value={filters.start_time || ''}
            onChange={(e) => handleFilterChange('start_time', e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        </Box>

        <Box sx={{ minWidth: 160, flex: '0 1 160px' }}>
          <TextField
            fullWidth
            size="small"
            type="datetime-local"
            label="End Time"
            value={filters.end_time || ''}
            onChange={(e) => handleFilterChange('end_time', e.target.value)}
            InputLabelProps={{ shrink: true }}
          />
        </Box>

        <Box sx={{ minWidth: 80, flex: '0 1 80px' }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<ClearIcon />}
            onClick={resetFilters}
            disabled={!hasActiveFilters}
            fullWidth
          >
            Clear
          </Button>
        </Box>
      </Box>
      
      {hasActiveFilters && (
        <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {filters.level_filter && (
            <Chip
              label={`Level: ${filters.level_filter}`}
              onDelete={() => handleFilterChange('level_filter', undefined)}
              size="small"
            />
          )}
          {filters.process_filter && (
            <Chip
              label={`Process: ${filters.process_filter}`}
              onDelete={() => handleFilterChange('process_filter', undefined)}
              size="small"
            />
          )}
          {filters.search && (
            <Chip
              label={`Search: "${filters.search}"`}
              onDelete={() => handleFilterChange('search', undefined)}
              size="small"
            />
          )}
          {filters.start_time && (
            <Chip
              label={`From: ${new Date(filters.start_time).toLocaleString()}`}
              onDelete={() => handleFilterChange('start_time', undefined)}
              size="small"
            />
          )}
          {filters.end_time && (
            <Chip
              label={`To: ${new Date(filters.end_time).toLocaleString()}`}
              onDelete={() => handleFilterChange('end_time', undefined)}
              size="small"
            />
          )}
        </Box>
      )}
    </Paper>
  );
};

export default LogsFilters;
