import type { LogsResponse, LogsQuery, LogLevel, LogProcesses } from '../types/logs';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://127.0.0.1:8000';

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async getLogs(query: LogsQuery = {}): Promise<LogsResponse> {
    const params = new URLSearchParams();
    
    if (query.page) params.append('page', query.page.toString());
    if (query.page_size) params.append('page_size', query.page_size.toString());
    if (query.level_filter) params.append('level_filter', query.level_filter);
    if (query.process_filter) params.append('process_filter', query.process_filter);
    if (query.search) params.append('search', query.search);
    if (query.start_time) params.append('start_time', query.start_time);
    if (query.end_time) params.append('end_time', query.end_time);

    const queryString = params.toString();
    const endpoint = `/api/logs${queryString ? `?${queryString}` : ''}`;
    
    return this.request<LogsResponse>(endpoint);
  }

  async getLogLevels(): Promise<LogLevel> {
    return this.request<LogLevel>('/api/logs/levels');
  }

  async getLogProcesses(): Promise<LogProcesses> {
    return this.request<LogProcesses>('/api/logs/processes');
  }

  async healthCheck(): Promise<{ status: string; service: string }> {
    return this.request<{ status: string; service: string }>('/api/health');
  }
}

export const apiClient = new ApiClient();
