export interface LogEntry {
  id: number;
  timestamp: string;
  process: string;
  position: number;
  level: string;
  message: string;
}

export interface LogsResponse {
  logs: LogEntry[];
  total: number;
  page: number;
  page_size: number;
  has_next: boolean;
}

export interface LogsQuery {
  page?: number;
  page_size?: number;
  level_filter?: string;
  process_filter?: string;
  search?: string;
  start_time?: string;
  end_time?: string;
}

export interface LogLevel {
  levels: string[];
}

export interface LogProcesses {
  processes: string[];
}
