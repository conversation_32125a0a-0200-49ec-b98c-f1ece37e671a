import { create } from 'zustand';
import type { LogsQuery } from '../types/logs';

interface LogsState {
  // Filter state
  filters: LogsQuery;
  
  // UI state
  selectedLogId: number | null;
  
  // Actions
  setFilters: (filters: Partial<LogsQuery>) => void;
  resetFilters: () => void;
  setSelectedLogId: (id: number | null) => void;
}

const defaultFilters: LogsQuery = {
  page: 1,
  page_size: 100,
  level_filter: undefined,
  process_filter: undefined,
  search: undefined,
  start_time: undefined,
  end_time: undefined,
};

export const useLogsStore = create<LogsState>((set) => ({
  filters: defaultFilters,
  selectedLogId: null,
  
  setFilters: (newFilters) => {
    set((state) => ({
      filters: {
        ...state.filters,
        ...newFilters,
        // Reset to page 1 when filters change (except when explicitly setting page)
        page: newFilters.page !== undefined ? newFilters.page : 1,
      },
    }));
  },
  
  resetFilters: () => {
    set({ filters: defaultFilters });
  },
  
  setSelectedLogId: (id) => {
    set({ selectedLogId: id });
  },
}));
