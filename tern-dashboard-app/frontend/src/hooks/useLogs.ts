import { useQuery } from '@tanstack/react-query';
import { apiClient } from '../api/client';
import type { LogsQuery } from '../types/logs';

export const useLogs = (query: LogsQuery) => {
  return useQuery({
    queryKey: ['logs', query],
    queryFn: () => apiClient.getLogs(query),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
};

export const useLogLevels = () => {
  return useQuery({
    queryKey: ['log-levels'],
    queryFn: () => apiClient.getLogLevels(),
    staleTime: 300000, // 5 minutes - levels don't change often
    refetchOnWindowFocus: false,
  });
};

export const useLogProcesses = () => {
  return useQuery({
    queryKey: ['log-processes'],
    queryFn: () => apiClient.getLogProcesses(),
    staleTime: 300000, // 5 minutes - processes don't change often
    refetchOnWindowFocus: false,
  });
};
