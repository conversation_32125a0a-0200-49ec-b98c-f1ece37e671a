#!/usr/bin/env python3
"""
Development script for Tern Dashboard App.

This script helps with development by providing commands to:
- Start the backend server
- Start the frontend dev server
- Start both in development mode
- Run the desktop app in development mode
"""

import os
import sys
import subprocess
import threading
import time
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"


def run_command_async(cmd, cwd=None, name="Process"):
    """Run a command asynchronously."""
    def run():
        print(f"Starting {name}...")
        try:
            subprocess.run(cmd, cwd=cwd, check=True)
        except subprocess.CalledProcessError as e:
            print(f"{name} failed with exit code {e.returncode}")
        except KeyboardInterrupt:
            print(f"{name} interrupted")
    
    thread = threading.Thread(target=run, daemon=True)
    thread.start()
    return thread


def start_backend():
    """Start the FastAPI backend server."""
    print("Starting backend server...")
    os.chdir(BACKEND_DIR)
    subprocess.run([
        sys.executable, "-m", "uvicorn", 
        "app.main:app", 
        "--reload", 
        "--host", "127.0.0.1", 
        "--port", "8000"
    ])


def start_frontend():
    """Start the Vite frontend dev server."""
    print("Starting frontend dev server...")
    os.chdir(FRONTEND_DIR)
    subprocess.run(["npm", "run", "dev"])


def start_desktop():
    """Start the desktop app."""
    print("Starting desktop app...")
    os.chdir(PROJECT_ROOT)
    subprocess.run([sys.executable, "desktop/entry.py"])


def start_dev():
    """Start both backend and frontend in development mode."""
    print("Starting development servers...")
    
    # Start backend in background
    backend_thread = run_command_async(
        [sys.executable, "-m", "uvicorn", "app.main:app", "--reload", "--host", "127.0.0.1", "--port", "8000"],
        cwd=BACKEND_DIR,
        name="Backend"
    )
    
    # Wait a bit for backend to start
    time.sleep(2)
    
    # Start frontend in background
    frontend_thread = run_command_async(
        ["npm", "run", "dev"],
        cwd=FRONTEND_DIR,
        name="Frontend"
    )
    
    print("Development servers started!")
    print("Backend: http://127.0.0.1:8000")
    print("Frontend: http://127.0.0.1:5173")
    print("API Docs: http://127.0.0.1:8000/docs")
    print("\nPress Ctrl+C to stop all servers")
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nStopping development servers...")


def install_deps():
    """Install all dependencies."""
    print("Installing backend dependencies...")
    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], cwd=BACKEND_DIR)
    
    print("Installing frontend dependencies...")
    subprocess.run(["npm", "install"], cwd=FRONTEND_DIR)
    
    print("Dependencies installed!")


def build_frontend():
    """Build the frontend for production."""
    print("Building frontend...")
    subprocess.run(["npm", "run", "build"], cwd=FRONTEND_DIR)
    print("Frontend built!")


def show_help():
    """Show help message."""
    print("""
Tern Dashboard Development Script

Usage: python dev.py <command>

Commands:
  backend     Start the FastAPI backend server
  frontend    Start the Vite frontend dev server  
  desktop     Start the desktop app
  dev         Start both backend and frontend in development mode
  install     Install all dependencies
  build       Build the frontend for production
  help        Show this help message

Examples:
  python dev.py dev       # Start development servers
  python dev.py desktop   # Run desktop app
  python dev.py install   # Install dependencies
""")


def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == "backend":
        start_backend()
    elif command == "frontend":
        start_frontend()
    elif command == "desktop":
        start_desktop()
    elif command == "dev":
        start_dev()
    elif command == "install":
        install_deps()
    elif command == "build":
        build_frontend()
    elif command == "help":
        show_help()
    else:
        print(f"Unknown command: {command}")
        show_help()
        sys.exit(1)


if __name__ == "__main__":
    main()
