#!/usr/bin/env python3
"""
Desktop entry point for Tern Dashboard App.

This script starts the FastAPI backend server and opens the frontend in a native window
using pywebview.
"""

import os
import sys
import time
import socket
import threading
import subprocess
from pathlib import Path
import uvicorn
import webview

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent / "backend"
sys.path.insert(0, str(backend_dir))

from app.main import app


def find_free_port(start_port=8000, max_attempts=100):
    """Find a free port starting from start_port."""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")


def wait_for_server(host, port, timeout=30):
    """Wait for the server to be ready."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex((host, port))
                if result == 0:
                    return True
        except Exception:
            pass
        time.sleep(0.1)
    return False


def start_backend_server(host="127.0.0.1", port=8000):
    """Start the FastAPI backend server in a separate thread."""
    def run_server():
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level="info",
            access_log=False,  # Reduce noise in desktop app
        )
    
    server_thread = threading.Thread(target=run_server, daemon=True)
    server_thread.start()
    return server_thread


def build_frontend_if_needed():
    """Build the frontend if dist directory doesn't exist."""
    frontend_dir = Path(__file__).parent.parent / "frontend"
    dist_dir = frontend_dir / "dist"
    
    if not dist_dir.exists():
        print("Frontend not built. Building now...")
        try:
            # Change to frontend directory and run build
            result = subprocess.run(
                ["npm", "run", "build"],
                cwd=frontend_dir,
                check=True,
                capture_output=True,
                text=True
            )
            print("Frontend built successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"Failed to build frontend: {e}")
            print(f"stdout: {e.stdout}")
            print(f"stderr: {e.stderr}")
            return False
        except FileNotFoundError:
            print("npm not found. Please install Node.js and npm.")
            return False
    
    return True


def main():
    """Main entry point for the desktop application."""
    print("Starting Tern Dashboard App...")
    
    # Build frontend if needed
    if not build_frontend_if_needed():
        print("Failed to build frontend. Exiting.")
        sys.exit(1)
    
    # Find a free port
    try:
        port = find_free_port()
        print(f"Using port {port}")
    except RuntimeError as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    # Start the backend server
    print("Starting backend server...")
    server_thread = start_backend_server(port=port)
    
    # Wait for server to be ready
    print("Waiting for server to start...")
    if not wait_for_server("127.0.0.1", port):
        print("Server failed to start within timeout period")
        sys.exit(1)
    
    print("Server is ready!")
    
    # Create the webview window
    url = f"http://127.0.0.1:{port}/ui/"
    
    print(f"Opening application at {url}")
    
    # Configure webview window
    window = webview.create_window(
        title="Tern Dashboard - Log Viewer",
        url=url,
        width=1400,
        height=900,
        min_size=(800, 600),
        resizable=True,
    )
    
    # Start the webview (this blocks until window is closed)
    webview.start(debug=False)
    
    print("Application closed.")


if __name__ == "__main__":
    main()
